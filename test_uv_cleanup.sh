#!/usr/bin/env zsh
# 测试脚本：验证 UV 缓存清理功能

# 模拟脚本中的相关变量和函数
typeset -gr UV_CLEANUP_DAY="Mon"
typeset -gA COMMAND_AVAILABLE
typeset -gA SCRIPT_STATS

# 颜色定义
if [[ -t 1 ]]; then
  typeset -gr GREEN='\033[0;32m' RED='\033[0;31m' YELLOW='\033[0;33m'
  typeset -gr BLUE='\033[0;34m' NC='\033[0m'
else
  typeset -gr GREEN='' RED='' YELLOW='' BLUE='' NC=''
fi

# 日志函数
log_time() { date +'%H:%M'; }
log() { echo -e "${BLUE}[$(log_time)] $*${NC}"; }
log_success() { echo -e "${GREEN}[$(log_time)] ✓ $*${NC}"; }
log_error() { echo -e "${RED}[$(log_time)] ✗ $*${NC}" >&2; }

# 检查 uv 命令是否可用
check_uv_command() {
  if command -v "uv" &>/dev/null; then
    COMMAND_AVAILABLE[uv]=true
    log_success "找到 uv 命令"
  else
    COMMAND_AVAILABLE[uv]=false
    log_error "未找到 uv 命令"
  fi
}

# 测试 UV 缓存清理逻辑（从原脚本中提取）
test_uv_cleanup() {
  echo "=== 测试 UV 缓存清理功能 ==="
  echo "当前日期: $(date +'%Y-%m-%d %a')"
  echo "预定清理日: $UV_CLEANUP_DAY"
  echo

  # UV 缓存清理
  if [[ "${COMMAND_AVAILABLE[uv]}" == "true" ]]; then
    if [[ "$(LC_ALL=C date +'%a')" == "$UV_CLEANUP_DAY" ]]; then
      log "今天是 $UV_CLEANUP_DAY，执行 UV 缓存清理"
      if uv cache clean &>/dev/null; then
        SCRIPT_STATS[uv_cache_status]="清理成功"
        log_success "UV 缓存清理完成"
      else
        SCRIPT_STATS[uv_cache_status]="清理失败"
        log_error "UV 缓存清理失败"
      fi
    else
      log "跳过 UV 缓存清理（非预定日）"
      SCRIPT_STATS[uv_cache_status]="未执行"
    fi
  else
    log "未找到 uv 命令，跳过 UV 缓存清理"
    SCRIPT_STATS[uv_cache_status]="跳过"
  fi

  echo
  echo "=== 测试结果 ==="
  echo "UV 缓存状态: ${SCRIPT_STATS[uv_cache_status]}"
}

# 测试不同日期的情况
test_different_days() {
  echo
  echo "=== 测试不同日期情况 ==="

  local test_days=("Mon" "Tue" "Wed" "Thu" "Fri" "Sat" "Sun")
  local current_day=$(LC_ALL=C date +'%a')

  for day in "${test_days[@]}"; do
    echo -n "如果今天是 $day: "
    if [[ "$day" == "$UV_CLEANUP_DAY" ]]; then
      echo -e "${GREEN}会执行 UV 缓存清理${NC}"
    else
      echo -e "${YELLOW}跳过 UV 缓存清理${NC}"
    fi
  done

  echo
  echo "实际今天是: $current_day"
  if [[ "$current_day" == "$UV_CLEANUP_DAY" ]]; then
    echo -e "${GREEN}✓ 今天会执行 UV 缓存清理${NC}"
  else
    echo -e "${YELLOW}! 今天不会执行 UV 缓存清理${NC}"
  fi
}

# 主函数
main() {
  echo "UV 缓存清理功能测试"
  echo "===================="
  echo

  check_uv_command
  test_uv_cleanup
  test_different_days

  echo
  echo "测试完成！"
}

main "$@"
